# Pagination System

The ho-trans backend implements a comprehensive pagination system that provides consistent pagination across all modules and endpoints.

## Overview

The pagination system is built around several key components:

- `PaginationParams` - Standard pagination parameters
- `PaginatedResponse` - Generic paginated response wrapper
- `Paginator` - Database pagination utility
- FastAPI dependencies for easy integration

## Core Components

### PaginationParams

Standard pagination parameters used across all endpoints:

```python
from src.pagination import PaginationParams

params = PaginationParams(page=2, limit=20)
print(params.offset)  # 20 (calculated automatically)
```

**Fields:**
- `page` (int): Page number (1-based, default: 1)
- `limit` (int): Items per page (1-100, default: 10)
- `offset` (property): Calculated offset for database queries

### PaginatedResponse

Generic response wrapper that includes both data and pagination metadata:

```python
from src.pagination import PaginatedResponse

response = PaginatedResponse.create(
    items=[...],
    total=100,
    params=PaginationParams(page=2, limit=10)
)
```

**Structure:**
```json
{
  "items": [...],
  "meta": {
    "page": 2,
    "limit": 10,
    "total": 100,
    "pages": 10,
    "has_prev": true,
    "has_next": true,
    "prev_page": 1,
    "next_page": 3
  }
}
```

### Paginator

Database utility for paginating SQLAlchemy queries:

```python
from src.pagination import Paginator

paginator = Paginator(database)
items, total = await paginator.paginate(query, params)
response = await paginator.paginate_response(query, params)
```

## FastAPI Integration

### Using Pagination Dependencies

```python
from fastapi import APIRouter, Depends
from src.pagination import PaginationParams, get_pagination_params

@router.get("/items", response_model=PaginatedResponse[ItemResponse])
async def get_items(
    pagination: PaginationParams = Depends(get_pagination_params)
):
    return await service.get_items_paginated(pagination)
```

### Query Parameters

The pagination dependency automatically extracts these query parameters:

- `page` - Page number (default: 1)
- `limit` - Items per page (default: 10, max: 100)

Example request:
```
GET /api/v1/projects?page=2&limit=20
```

## Service Layer Integration

### Basic Pagination

```python
from src.pagination import PaginationParams, PaginatedResponse, Paginator

class ProjectService:
    def __init__(self, database: Database):
        self.database = database
        self.paginator = Paginator(database)
    
    async def get_projects_paginated(
        self, 
        params: PaginationParams
    ) -> PaginatedResponse[ProjectResponse]:
        query = select(Project).order_by(Project.created_at.desc())
        items, total = await self.paginator.paginate(query, params)
        project_responses = [ProjectResponse(**item) for item in items]
        return PaginatedResponse.create(project_responses, total, params)
```

### Advanced Pagination with Filtering

```python
async def get_projects_paginated(
    self,
    params: PaginationParams,
    status: Optional[str] = None,
    search: Optional[str] = None
) -> PaginatedResponse[ProjectResponse]:
    query = select(Project)
    
    # Apply filters
    if status:
        query = query.where(Project.status == status)
    
    if search:
        query = query.where(Project.name.ilike(f"%{search}%"))
    
    # Apply ordering
    query = query.order_by(Project.created_at.desc())
    
    # Paginate
    items, total = await self.paginator.paginate(query, params)
    project_responses = [ProjectResponse(**item) for item in items]
    
    return PaginatedResponse.create(project_responses, total, params)
```

## Module Implementation Status

### ✅ Projects Module
- `GET /api/v1/projects` - Paginated project listing
- Uses `get_projects_paginated()` service method
- Returns `PaginatedResponse[ProjectResponse]`

### ✅ Translation Module  
- `GET /api/v1/translation/templates` - Paginated template listing
- Uses `get_translation_templates_paginated()` service method
- Returns `PaginatedResponse[TranslationTemplateResponse]`

### 🔄 OCR Module
- Ready for pagination implementation
- Can add paginated job listings

### 🔄 LLM Providers Module
- Ready for pagination implementation
- Can add paginated batch operation results

## Utility Functions

### Quick Response Creation

```python
from src.pagination import create_paginated_response

response = create_paginated_response(
    items=items,
    total=total_count,
    page=page_number,
    limit=page_size
)
```

### Pagination Info

```python
from src.pagination import get_pagination_info

info = get_pagination_info(total=100, page=2, limit=10)
# Returns dict with pagination metadata
```

## Search and Filtering Integration

The pagination system can be combined with search and filtering:

```python
from src.pagination import SearchParams, get_search_params

@router.get("/items")
async def get_items(
    pagination: PaginationParams = Depends(get_pagination_params),
    search: SearchParams = Depends(get_search_params)
):
    return await service.search_items(pagination, search)
```

Query parameters:
```
GET /api/v1/items?page=2&limit=20&q=search_term&sort_by=name&sort_order=desc
```

## Best Practices

### 1. Consistent Response Format
Always use `PaginatedResponse[T]` for paginated endpoints:

```python
@router.get("/items", response_model=PaginatedResponse[ItemResponse])
```

### 2. Service Layer Pagination
Implement pagination in the service layer, not the router:

```python
# ✅ Good
async def get_items_paginated(self, params: PaginationParams):
    return await self.paginator.paginate_response(query, params)

# ❌ Avoid
async def get_items(self, limit: int, offset: int):
    # Manual pagination logic
```

### 3. Database Query Optimization
Use proper indexing and ordering for paginated queries:

```python
# Add ordering for consistent pagination
query = query.order_by(Model.created_at.desc())

# Use database indexes for filtered pagination
query = query.where(Model.status == status)  # Ensure status is indexed
```

### 4. Limit Validation
The system automatically enforces a maximum limit of 100 items per page to prevent performance issues.

### 5. Error Handling
Handle edge cases gracefully:

```python
# Empty results
if total == 0:
    return PaginatedResponse.create([], 0, params)

# Page beyond available data
if params.page > total_pages:
    # Return empty page or redirect to last page
```

## Testing

The pagination system includes comprehensive tests:

```python
# Test pagination parameters
def test_pagination_params():
    params = PaginationParams(page=3, limit=20)
    assert params.offset == 40

# Test paginated response
def test_paginated_response():
    response = PaginatedResponse.create(items, total, params)
    assert response.meta.has_next == expected_has_next
```

Run pagination tests:
```bash
pytest tests/test_pagination.py -v
```

## Migration Guide

### Updating Existing Endpoints

1. **Update Router:**
```python
# Before
@router.get("/items", response_model=List[ItemResponse])
async def get_items(limit: int = 10, offset: int = 0):

# After  
@router.get("/items", response_model=PaginatedResponse[ItemResponse])
async def get_items(pagination: PaginationParams = Depends(get_pagination_params)):
```

2. **Update Service:**
```python
# Before
async def get_items(self, limit: int, offset: int):
    query = select(Item).limit(limit).offset(offset)
    return await self.database.fetch_all(query)

# After
async def get_items_paginated(self, params: PaginationParams):
    query = select(Item).order_by(Item.created_at.desc())
    items, total = await self.paginator.paginate(query, params)
    responses = [ItemResponse(**item) for item in items]
    return PaginatedResponse.create(responses, total, params)
```

3. **Update Frontend:**
```typescript
// Before
interface ItemsResponse {
  items: Item[];
}

// After
interface PaginatedItemsResponse {
  items: Item[];
  meta: {
    page: number;
    limit: number;
    total: number;
    pages: number;
    has_prev: boolean;
    has_next: boolean;
    prev_page?: number;
    next_page?: number;
  };
}
```
