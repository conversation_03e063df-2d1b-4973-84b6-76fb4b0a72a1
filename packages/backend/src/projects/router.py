"""
FastAPI router for project management endpoints.
"""
from typing import List, Dict, Any

from fastapi import APIRouter, Depends, status, UploadFile, File, Form
from fastapi.responses import JSONResponse

from src.projects.dependencies import (
    get_project_service,
    valid_project_id,
    valid_project_page_id,
    valid_text_region_id,
    valid_project_and_page,
    valid_page_and_region
)
from src.projects.service import ProjectService
from src.projects.schemas import (
    ProjectCreate,
    ProjectUpdate,
    ProjectResponse,
    ProjectDetailResponse,
    ProjectPageCreate,
    ProjectPageResponse,
    ProjectPageDetailResponse,
    TextRegionCreate,
    TextRegionUpdate,
    TextRegionResponse,
    PaginationParams,
    PaginatedResponse,
    SuccessResponse
)
from src.schemas import PaginationParams

router = APIRouter()


# Project endpoints
@router.post(
    "/",
    response_model=ProjectResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create a new project",
    description="Create a new manga translation project"
)
async def create_project(
    project_data: ProjectCreate,
    service: ProjectService = Depends(get_project_service)
):
    """Create a new project."""
    return await service.create_project(project_data)


@router.get(
    "/",
    response_model=List[ProjectResponse],
    summary="Get all projects",
    description="Get all projects with pagination"
)
async def get_projects(
    pagination: PaginationParams = Depends(),
    service: ProjectService = Depends(get_project_service)
):
    """Get all projects."""
    return await service.get_projects(
        limit=pagination.limit,
        offset=pagination.offset
    )


@router.get(
    "/{project_id}",
    response_model=ProjectResponse,
    summary="Get project by ID",
    description="Get a specific project by its ID"
)
async def get_project(
    project: ProjectResponse = Depends(valid_project_id)
):
    """Get project by ID."""
    return project


@router.get(
    "/{project_id}/detail",
    response_model=ProjectDetailResponse,
    summary="Get detailed project information",
    description="Get detailed project information including pages"
)
async def get_project_detail(
    project_id: str,
    service: ProjectService = Depends(get_project_service)
):
    """Get detailed project information."""
    return await service.get_project_detail(project_id)


@router.put(
    "/{project_id}",
    response_model=ProjectResponse,
    summary="Update project",
    description="Update project information"
)
async def update_project(
    project_id: str,
    project_data: ProjectUpdate,
    service: ProjectService = Depends(get_project_service)
):
    """Update project."""
    return await service.update_project(project_id, project_data)


@router.delete(
    "/{project_id}",
    response_model=SuccessResponse,
    summary="Delete project",
    description="Delete a project and all its pages"
)
async def delete_project(
    project_id: str,
    service: ProjectService = Depends(get_project_service)
):
    """Delete project."""
    await service.delete_project(project_id)
    return SuccessResponse(message="Project deleted successfully")


# Project page endpoints
@router.post(
    "/{project_id}/pages",
    response_model=ProjectPageResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Upload a new page",
    description="Upload a new page to a project"
)
async def upload_project_page(
    project_id: str,
    file: UploadFile = File(...),
    page_number: int = Form(...),
    project: ProjectResponse = Depends(valid_project_id),
    service: ProjectService = Depends(get_project_service)
):
    """Upload a new page to a project."""
    # TODO: Implement file upload logic
    # For now, create a placeholder page
    page_data = ProjectPageCreate(
        page_number=page_number,
        original_filename=file.filename or "unknown"
    )
    
    return await service.create_project_page(
        project_id=project_id,
        page_data=page_data,
        file_path=f"uploads/{project_id}/{file.filename}",
        file_size=0,  # TODO: Get actual file size
        image_width=None,
        image_height=None
    )


@router.get(
    "/{project_id}/pages",
    response_model=List[ProjectPageResponse],
    summary="Get project pages",
    description="Get all pages for a project"
)
async def get_project_pages(
    project_id: str,
    project: ProjectResponse = Depends(valid_project_id),
    service: ProjectService = Depends(get_project_service)
):
    """Get all pages for a project."""
    return await service.get_project_pages(project_id)


@router.get(
    "/{project_id}/pages/{page_id}",
    response_model=ProjectPageResponse,
    summary="Get project page",
    description="Get a specific project page"
)
async def get_project_page(
    page: ProjectPageResponse = Depends(valid_project_page_id)
):
    """Get project page."""
    return page


@router.get(
    "/{project_id}/pages/{page_id}/detail",
    response_model=ProjectPageDetailResponse,
    summary="Get detailed page information",
    description="Get detailed page information including text regions"
)
async def get_project_page_detail(
    page_id: str,
    validation: Dict[str, Any] = Depends(valid_project_and_page),
    service: ProjectService = Depends(get_project_service)
):
    """Get detailed page information."""
    return await service.get_project_page_detail(page_id)


@router.delete(
    "/{project_id}/pages/{page_id}",
    response_model=SuccessResponse,
    summary="Delete project page",
    description="Delete a project page and all its text regions"
)
async def delete_project_page(
    page_id: str,
    validation: Dict[str, Any] = Depends(valid_project_and_page),
    service: ProjectService = Depends(get_project_service)
):
    """Delete project page."""
    await service.delete_project_page(page_id)
    return SuccessResponse(message="Project page deleted successfully")


# Text region endpoints
@router.post(
    "/{project_id}/pages/{page_id}/regions",
    response_model=TextRegionResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create text region",
    description="Create a new text region on a page"
)
async def create_text_region(
    page_id: str,
    region_data: TextRegionCreate,
    _validation: Dict[str, Any] = Depends(valid_project_and_page),
    service: ProjectService = Depends(get_project_service)
):
    """Create a new text region."""
    return await service.create_text_region(page_id, region_data)


@router.get(
    "/{project_id}/pages/{page_id}/regions",
    response_model=List[TextRegionResponse],
    summary="Get page text regions",
    description="Get all text regions for a page"
)
async def get_page_text_regions(
    page_id: str,
    _validation: Dict[str, Any] = Depends(valid_project_and_page),
    service: ProjectService = Depends(get_project_service)
):
    """Get all text regions for a page."""
    return await service.get_page_text_regions(page_id)


@router.get(
    "/{project_id}/pages/{page_id}/regions/{region_id}",
    response_model=TextRegionResponse,
    summary="Get text region",
    description="Get a specific text region"
)
async def get_text_region(
    region: TextRegionResponse = Depends(valid_text_region_id)
):
    """Get text region."""
    return region


@router.put(
    "/{project_id}/pages/{page_id}/regions/{region_id}",
    response_model=TextRegionResponse,
    summary="Update text region",
    description="Update text region information"
)
async def update_text_region(
    region_id: str,
    region_data: TextRegionUpdate,
    _validation: Dict[str, Any] = Depends(valid_page_and_region),
    service: ProjectService = Depends(get_project_service)
):
    """Update text region."""
    return await service.update_text_region(region_id, region_data)


@router.delete(
    "/{project_id}/pages/{page_id}/regions/{region_id}",
    response_model=SuccessResponse,
    summary="Delete text region",
    description="Delete a text region"
)
async def delete_text_region(
    region_id: str,
    _validation: Dict[str, Any] = Depends(valid_page_and_region),
    service: ProjectService = Depends(get_project_service)
):
    """Delete text region."""
    await service.delete_text_region(region_id)
    return SuccessResponse(message="Text region deleted successfully")
