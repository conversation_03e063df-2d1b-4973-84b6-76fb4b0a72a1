"""
SQLAlchemy models for OCR processing.
"""
from sqlalchemy import Column, String, Text, Float, Integer, ForeignKey, JSON
from sqlalchemy.orm import relationship

from src.constants import OCRStatus, LLMProvider
from src.models import BaseModel


class OCRJob(BaseModel):
    """OCR job model for tracking OCR processing tasks."""
    
    __tablename__ = "ocr_job"
    
    page_id = Column(String(36), ForeignKey("project_page.id"), nullable=False)
    status = Column(String(50), default=OCRStatus.PENDING, nullable=False)
    provider = Column(String(50), default=LLMProvider.CLAUDE, nullable=False)
    
    # Processing details
    prompt_used = Column(Text, nullable=True)
    raw_response = Column(Text, nullable=True)
    processing_time_seconds = Column(Float, nullable=True)
    
    # Error handling
    error_message = Column(Text, nullable=True)
    retry_count = Column(Integer, default=0, nullable=False)
    
    # Results metadata
    total_regions_detected = Column(Integer, default=0, nullable=False)
    average_confidence = Column(Float, nullable=True)
    
    # Relationships
    page = relationship("ProjectPage", foreign_keys=[page_id])
    results = relationship("OCRResult", back_populates="job", cascade="all, delete-orphan")


class OCRResult(BaseModel):
    """OCR result model for individual text detection results."""
    
    __tablename__ = "ocr_result"
    
    job_id = Column(String(36), ForeignKey("ocr_job.id"), nullable=False)
    
    # Text content
    detected_text = Column(Text, nullable=False)
    confidence_score = Column(Float, nullable=True)
    
    # Region information
    region_type = Column(String(50), nullable=True)
    
    # Bounding box coordinates (normalized 0-1)
    x = Column(Float, nullable=True)
    y = Column(Float, nullable=True)
    width = Column(Float, nullable=True)
    height = Column(Float, nullable=True)
    
    # Additional metadata from LLM
    metadata = Column(JSON, nullable=True)
    
    # Relationships
    job = relationship("OCRJob", back_populates="results")
