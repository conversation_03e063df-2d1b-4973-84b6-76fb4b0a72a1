"""
FastAPI router for OCR processing endpoints.
"""
from typing import List, Optional

from fastapi import APIRouter, Depends, status, BackgroundTasks

from src.ocr.dependencies import get_ocr_service, valid_ocr_job_id
from src.ocr.service import OCRService
from src.ocr.schemas import (
    OCRJobCreate,
    OCRJobResponse,
    OCRJobDetailResponse,
    OCRResultResponse,
    OCRProcessRequest,
    OCRBatchProcessRequest,
    OCRStatistics
)
from src.pagination import PaginationParams, PaginatedResponse, get_pagination_params
from src.schemas import SuccessResponse

router = APIRouter()


# OCR Job endpoints
@router.post(
    "/jobs",
    response_model=OCRJobResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create OCR job",
    description="Create a new OCR processing job"
)
async def create_ocr_job(
    job_data: OCRJobCreate,
    service: OCRService = Depends(get_ocr_service)
):
    """Create a new OCR job."""
    return await service.create_ocr_job(job_data)


@router.get(
    "/jobs/{job_id}",
    response_model=OCRJobResponse,
    summary="Get OCR job",
    description="Get OCR job information by ID"
)
async def get_ocr_job(
    job: OCRJobResponse = Depends(valid_ocr_job_id)
):
    """Get OCR job by ID."""
    return job


@router.get(
    "/jobs/{job_id}/detail",
    response_model=OCRJobDetailResponse,
    summary="Get detailed OCR job information",
    description="Get detailed OCR job information including results"
)
async def get_ocr_job_detail(
    job_id: str,
    service: OCRService = Depends(get_ocr_service)
):
    """Get detailed OCR job information."""
    return await service.get_ocr_job_detail(job_id)


@router.get(
    "/jobs/{job_id}/results",
    response_model=List[OCRResultResponse],
    summary="Get OCR results",
    description="Get all OCR results for a job"
)
async def get_ocr_results(
    job_id: str,
    _job: OCRJobResponse = Depends(valid_ocr_job_id),
    service: OCRService = Depends(get_ocr_service)
):
    """Get OCR results for a job."""
    return await service.get_ocr_results(job_id)


@router.get(
    "/pages/{page_id}/jobs",
    response_model=List[OCRJobResponse],
    summary="Get page OCR jobs",
    description="Get all OCR jobs for a page"
)
async def get_page_ocr_jobs(
    page_id: str,
    service: OCRService = Depends(get_ocr_service)
):
    """Get all OCR jobs for a page."""
    return await service.get_ocr_jobs_by_page(page_id)


# OCR Processing endpoints
@router.post(
    "/process",
    response_model=OCRJobResponse,
    status_code=status.HTTP_202_ACCEPTED,
    summary="Process OCR request",
    description="Start OCR processing for a page"
)
async def process_ocr(
    request: OCRProcessRequest,
    background_tasks: BackgroundTasks,
    service: OCRService = Depends(get_ocr_service)
):
    """Process OCR request."""
    job = await service.process_ocr_request(request)

    # Add background task for actual processing
    # background_tasks.add_task(process_ocr_background, job.id, service)

    return job


@router.post(
    "/process/batch",
    response_model=List[OCRJobResponse],
    status_code=status.HTTP_202_ACCEPTED,
    summary="Process batch OCR request",
    description="Start OCR processing for multiple pages"
)
async def process_batch_ocr(
    request: OCRBatchProcessRequest,
    background_tasks: BackgroundTasks,
    service: OCRService = Depends(get_ocr_service)
):
    """Process batch OCR request."""
    # TODO: Implement batch processing logic
    # For now, return empty list
    return []


# Statistics endpoints
@router.get(
    "/statistics",
    response_model=OCRStatistics,
    summary="Get OCR statistics",
    description="Get OCR processing statistics"
)
async def get_ocr_statistics(
    project_id: Optional[str] = None,
    service: OCRService = Depends(get_ocr_service)
):
    """Get OCR statistics."""
    return await service.get_ocr_statistics(project_id)


# Utility endpoints
@router.post(
    "/jobs/{job_id}/retry",
    response_model=OCRJobResponse,
    summary="Retry OCR job",
    description="Retry a failed OCR job"
)
async def retry_ocr_job(
    job_id: str,
    background_tasks: BackgroundTasks,
    _job: OCRJobResponse = Depends(valid_ocr_job_id),
    service: OCRService = Depends(get_ocr_service)
):
    """Retry a failed OCR job."""
    # TODO: Implement retry logic
    # For now, just return the job
    return await service.get_ocr_job(job_id)


# Background task function (placeholder)
async def process_ocr_background(job_id: str, service: OCRService):
    """Background task for OCR processing."""
    # TODO: Implement actual OCR processing logic
    # This would involve:
    # 1. Loading the image file
    # 2. Calling the LLM provider
    # 3. Parsing the response
    # 4. Saving the results
    # 5. Updating the job status
    pass
