"""
Business logic for OCR processing.
"""
import json
import time
from typing import List, Optional, Dict, Any
from uuid import uuid4

from databases import Database
from sqlalchemy import select, insert, update, delete, func, and_

from src.ocr.models import OCRJob, OCRResult
from src.ocr.schemas import (
    OCRJobCreate, OCRJobResponse, OCRJobDetailResponse,
    OCRResultCreate, OCRResultResponse,
    OCRProcessRequest, OCRBatchProcessRequest,
    OCRStatistics, LLMOCRResponse, LLMOCRRegion
)
from src.ocr.exceptions import OCRJobNotFound, OCRProcessingFailed
from src.constants import OCRStatus, LLMProvider, DEFAULT_OCR_PROMPT
from src.config import settings


class OCRService:
    """Service class for OCR processing operations."""
    
    def __init__(self, database: Database):
        self.database = database
    
    async def create_ocr_job(self, job_data: OCRJobCreate) -> OCRJobResponse:
        """Create a new OCR job."""
        job_id = str(uuid4())
        
        query = insert(OCRJob).values(
            id=job_id,
            page_id=job_data.page_id,
            provider=job_data.provider,
            prompt_used=job_data.custom_prompt or DEFAULT_OCR_PROMPT,
            status=OCRStatus.PENDING
        )
        
        await self.database.execute(query)
        return await self.get_ocr_job(job_id)
    
    async def get_ocr_job(self, job_id: str) -> OCRJobResponse:
        """Get an OCR job by ID."""
        query = select(OCRJob).where(OCRJob.id == job_id)
        result = await self.database.fetch_one(query)
        
        if not result:
            raise OCRJobNotFound(f"OCR job with ID {job_id} not found")
        
        return OCRJobResponse(**dict(result))
    
    async def get_ocr_jobs_by_page(self, page_id: str) -> List[OCRJobResponse]:
        """Get all OCR jobs for a page."""
        query = select(OCRJob).where(OCRJob.page_id == page_id).order_by(OCRJob.created_at.desc())
        results = await self.database.fetch_all(query)
        return [OCRJobResponse(**dict(result)) for result in results]
    
    async def get_ocr_job_detail(self, job_id: str) -> OCRJobDetailResponse:
        """Get detailed OCR job information with results."""
        job = await self.get_ocr_job(job_id)
        results = await self.get_ocr_results(job_id)
        
        return OCRJobDetailResponse(
            **job.model_dump(),
            results=results
        )
    
    async def get_ocr_results(self, job_id: str) -> List[OCRResultResponse]:
        """Get all OCR results for a job."""
        query = select(OCRResult).where(OCRResult.job_id == job_id).order_by(OCRResult.created_at)
        results = await self.database.fetch_all(query)
        return [OCRResultResponse(**dict(result)) for result in results]
    
    async def update_ocr_job_status(
        self, 
        job_id: str, 
        status: OCRStatus,
        error_message: Optional[str] = None,
        processing_time: Optional[float] = None
    ) -> OCRJobResponse:
        """Update OCR job status."""
        update_data = {"status": status}
        
        if error_message:
            update_data["error_message"] = error_message
        
        if processing_time:
            update_data["processing_time_seconds"] = processing_time
        
        query = update(OCRJob).where(OCRJob.id == job_id).values(**update_data)
        await self.database.execute(query)
        
        return await self.get_ocr_job(job_id)
    
    async def save_ocr_results(self, job_id: str, results: List[OCRResultCreate]) -> List[OCRResultResponse]:
        """Save OCR results for a job."""
        saved_results = []
        
        for result_data in results:
            result_id = str(uuid4())
            
            query = insert(OCRResult).values(
                id=result_id,
                job_id=job_id,
                detected_text=result_data.detected_text,
                confidence_score=result_data.confidence_score,
                region_type=result_data.region_type,
                x=result_data.x,
                y=result_data.y,
                width=result_data.width,
                height=result_data.height,
                metadata=result_data.metadata
            )
            
            await self.database.execute(query)
            
            # Get the saved result
            result_query = select(OCRResult).where(OCRResult.id == result_id)
            saved_result = await self.database.fetch_one(result_query)
            saved_results.append(OCRResultResponse(**dict(saved_result)))
        
        # Update job statistics
        await self._update_job_statistics(job_id)
        
        return saved_results
    
    async def _update_job_statistics(self, job_id: str):
        """Update job statistics after saving results."""
        # Count total regions and calculate average confidence
        stats_query = select(
            func.count(OCRResult.id).label("total_regions"),
            func.avg(OCRResult.confidence_score).label("avg_confidence")
        ).where(OCRResult.job_id == job_id)
        
        stats = await self.database.fetch_one(stats_query)
        
        update_query = update(OCRJob).where(OCRJob.id == job_id).values(
            total_regions_detected=stats["total_regions"] or 0,
            average_confidence=stats["avg_confidence"]
        )
        
        await self.database.execute(update_query)
    
    async def process_ocr_request(self, request: OCRProcessRequest) -> OCRJobResponse:
        """Process an OCR request."""
        # Create OCR job
        job_data = OCRJobCreate(
            page_id=request.page_id,
            provider=request.provider or LLMProvider.CLAUDE,
            custom_prompt=request.custom_prompt
        )
        
        job = await self.create_ocr_job(job_data)
        
        # Start processing (this would be done asynchronously in a real implementation)
        # For now, we'll just mark it as processing
        await self.update_ocr_job_status(job.id, OCRStatus.PROCESSING)
        
        return job
    
    async def get_ocr_statistics(self, project_id: Optional[str] = None) -> OCRStatistics:
        """Get OCR processing statistics."""
        base_query = select(OCRJob)
        
        if project_id:
            # Join with ProjectPage to filter by project
            from src.projects.models import ProjectPage
            base_query = base_query.join(ProjectPage).where(ProjectPage.project_id == project_id)
        
        # Count jobs by status
        status_counts = {}
        for status in OCRStatus:
            count_query = base_query.where(OCRJob.status == status)
            count = await self.database.fetch_val(select(func.count()).select_from(count_query.subquery()))
            status_counts[status] = count or 0
        
        # Calculate averages
        avg_time_query = select(func.avg(OCRJob.processing_time_seconds)).select_from(base_query.subquery())
        avg_confidence_query = select(func.avg(OCRJob.average_confidence)).select_from(base_query.subquery())
        total_regions_query = select(func.sum(OCRJob.total_regions_detected)).select_from(base_query.subquery())
        
        avg_time = await self.database.fetch_val(avg_time_query)
        avg_confidence = await self.database.fetch_val(avg_confidence_query)
        total_regions = await self.database.fetch_val(total_regions_query)
        
        return OCRStatistics(
            total_jobs=sum(status_counts.values()),
            completed_jobs=status_counts.get(OCRStatus.COMPLETED, 0),
            failed_jobs=status_counts.get(OCRStatus.FAILED, 0),
            pending_jobs=status_counts.get(OCRStatus.PENDING, 0),
            processing_jobs=status_counts.get(OCRStatus.PROCESSING, 0),
            average_processing_time=avg_time,
            total_regions_detected=total_regions or 0,
            average_confidence=avg_confidence
        )
