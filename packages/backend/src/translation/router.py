"""
FastAPI router for translation processing endpoints.
"""
from typing import List, Optional

from fastapi import APIRouter, Depends, status, BackgroundTasks, Query

from src.translation.dependencies import (
    get_translation_service,
    valid_translation_job_id,
    valid_translation_template_id
)
from src.translation.service import TranslationService
from src.translation.schemas import (
    TranslationJobCreate,
    TranslationJobResponse,
    TranslationJobDetailResponse,
    TranslationAlternativeResponse,
    TranslationTemplateCreate,
    TranslationTemplateUpdate,
    TranslationTemplateResponse,
    TranslationProcessRequest,
    TranslationBatchProcessRequest,
    TranslationStatistics
)
from src.pagination import PaginationParams, PaginatedResponse, get_pagination_params
from src.schemas import SuccessResponse

router = APIRouter()


# Translation Job endpoints
@router.post(
    "/jobs",
    response_model=TranslationJobResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create translation job",
    description="Create a new translation processing job"
)
async def create_translation_job(
    job_data: TranslationJobCreate,
    service: TranslationService = Depends(get_translation_service)
):
    """Create a new translation job."""
    return await service.create_translation_job(job_data)


@router.get(
    "/jobs/{job_id}",
    response_model=TranslationJobResponse,
    summary="Get translation job",
    description="Get translation job information by ID"
)
async def get_translation_job(
    job: TranslationJobResponse = Depends(valid_translation_job_id)
):
    """Get translation job by ID."""
    return job


@router.get(
    "/jobs/{job_id}/detail",
    response_model=TranslationJobDetailResponse,
    summary="Get detailed translation job information",
    description="Get detailed translation job information including alternatives"
)
async def get_translation_job_detail(
    job_id: str,
    service: TranslationService = Depends(get_translation_service)
):
    """Get detailed translation job information."""
    return await service.get_translation_job_detail(job_id)


@router.get(
    "/jobs/{job_id}/alternatives",
    response_model=List[TranslationAlternativeResponse],
    summary="Get translation alternatives",
    description="Get all translation alternatives for a job"
)
async def get_translation_alternatives(
    job_id: str,
    _job: TranslationJobResponse = Depends(valid_translation_job_id),
    service: TranslationService = Depends(get_translation_service)
):
    """Get translation alternatives for a job."""
    return await service.get_translation_alternatives(job_id)


@router.post(
    "/jobs/{job_id}/alternatives/{alternative_id}/select",
    response_model=TranslationJobResponse,
    summary="Select translation alternative",
    description="Select a translation alternative as the primary translation"
)
async def select_translation_alternative(
    job_id: str,
    alternative_id: str,
    _job: TranslationJobResponse = Depends(valid_translation_job_id),
    service: TranslationService = Depends(get_translation_service)
):
    """Select a translation alternative."""
    return await service.select_translation_alternative(job_id, alternative_id)


@router.get(
    "/regions/{text_region_id}/jobs",
    response_model=List[TranslationJobResponse],
    summary="Get region translation jobs",
    description="Get all translation jobs for a text region"
)
async def get_region_translation_jobs(
    text_region_id: str,
    service: TranslationService = Depends(get_translation_service)
):
    """Get all translation jobs for a text region."""
    return await service.get_translation_jobs_by_region(text_region_id)


# Translation Processing endpoints
@router.post(
    "/process",
    response_model=TranslationJobResponse,
    status_code=status.HTTP_202_ACCEPTED,
    summary="Process translation request",
    description="Start translation processing for a text region"
)
async def process_translation(
    request: TranslationProcessRequest,
    background_tasks: BackgroundTasks,
    service: TranslationService = Depends(get_translation_service)
):
    """Process translation request."""
    job = await service.process_translation_request(request)

    # Add background task for actual processing
    # background_tasks.add_task(process_translation_background, job.id, service)

    return job


@router.post(
    "/process/batch",
    response_model=List[TranslationJobResponse],
    status_code=status.HTTP_202_ACCEPTED,
    summary="Process batch translation request",
    description="Start translation processing for multiple text regions"
)
async def process_batch_translation(
    request: TranslationBatchProcessRequest,
    background_tasks: BackgroundTasks,
    service: TranslationService = Depends(get_translation_service)
):
    """Process batch translation request."""
    # TODO: Implement batch processing logic
    # For now, return empty list
    return []


# Translation Template endpoints
@router.post(
    "/templates",
    response_model=TranslationTemplateResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create translation template",
    description="Create a new translation template"
)
async def create_translation_template(
    template_data: TranslationTemplateCreate,
    service: TranslationService = Depends(get_translation_service)
):
    """Create a new translation template."""
    return await service.create_translation_template(template_data)


@router.get(
    "/templates",
    response_model=PaginatedResponse[TranslationTemplateResponse],
    summary="Get translation templates",
    description="Get translation templates with optional filtering"
)
async def get_translation_templates(
    source_language: Optional[str] = Query(
        None, description="Filter by source language"),
    target_language: Optional[str] = Query(
        None, description="Filter by target language"),
    category: Optional[str] = Query(None, description="Filter by category"),
    pagination: PaginationParams = Depends(get_pagination_params),
    service: TranslationService = Depends(get_translation_service)
):
    """Get translation templates."""
    return await service.get_translation_templates_paginated(
        source_language=source_language,
        target_language=target_language,
        category=category,
        params=pagination
    )


@router.get(
    "/templates/{template_id}",
    response_model=TranslationTemplateResponse,
    summary="Get translation template",
    description="Get translation template by ID"
)
async def get_translation_template(
    template: TranslationTemplateResponse = Depends(
        valid_translation_template_id)
):
    """Get translation template by ID."""
    return template


@router.put(
    "/templates/{template_id}",
    response_model=TranslationTemplateResponse,
    summary="Update translation template",
    description="Update translation template information"
)
async def update_translation_template(
    template_id: str,
    template_data: TranslationTemplateUpdate,
    service: TranslationService = Depends(get_translation_service)
):
    """Update translation template."""
    return await service.update_translation_template(template_id, template_data)


@router.delete(
    "/templates/{template_id}",
    response_model=SuccessResponse,
    summary="Delete translation template",
    description="Delete a translation template"
)
async def delete_translation_template(
    template_id: str,
    service: TranslationService = Depends(get_translation_service)
):
    """Delete translation template."""
    await service.delete_translation_template(template_id)
    return SuccessResponse(message="Translation template deleted successfully")


# Statistics endpoints
@router.get(
    "/statistics",
    response_model=TranslationStatistics,
    summary="Get translation statistics",
    description="Get translation processing statistics"
)
async def get_translation_statistics(
    project_id: Optional[str] = Query(
        None, description="Filter by project ID"),
    service: TranslationService = Depends(get_translation_service)
):
    """Get translation statistics."""
    return await service.get_translation_statistics(project_id)
