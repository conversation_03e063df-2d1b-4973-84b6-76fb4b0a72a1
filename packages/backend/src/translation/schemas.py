"""
Pydantic schemas for translation processing.
"""
from typing import List, Optional, Dict, Any

from pydantic import Field, validator

from src.constants import TranslationStatus, LLMProvider, SUPPORTED_LANGUAGES
from src.schemas import BaseSchema, CustomModel


# Translation Job schemas
class TranslationJobCreate(CustomModel):
    """Schema for creating a new translation job."""
    
    text_region_id: str = Field(..., description="Text region ID")
    provider: LLMProvider = Field(default=LLMProvider.CLAUDE, description="LLM provider to use")
    source_language: str = Field(..., description="Source language")
    target_language: str = Field(..., description="Target language")
    original_text: str = Field(..., description="Original text to translate")
    custom_prompt: Optional[str] = Field(None, description="Custom prompt for translation")
    
    @validator("source_language", "target_language")
    def validate_language(cls, v):
        if v not in SUPPORTED_LANGUAGES:
            raise ValueError(f"Unsupported language: {v}")
        return v


class TranslationJobResponse(BaseSchema):
    """Schema for translation job response."""
    
    text_region_id: str = Field(..., description="Text region ID")
    status: TranslationStatus = Field(..., description="Translation status")
    provider: LLMProvider = Field(..., description="LLM provider used")
    source_language: str = Field(..., description="Source language")
    target_language: str = Field(..., description="Target language")
    original_text: str = Field(..., description="Original text")
    translated_text: Optional[str] = Field(None, description="Translated text")
    prompt_used: Optional[str] = Field(None, description="Prompt used for translation")
    processing_time_seconds: Optional[float] = Field(None, description="Processing time in seconds")
    confidence_score: Optional[float] = Field(None, description="Translation confidence score")
    quality_score: Optional[float] = Field(None, description="Translation quality score")
    error_message: Optional[str] = Field(None, description="Error message if failed")
    retry_count: int = Field(default=0, description="Number of retry attempts")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


# Translation Alternative schemas
class TranslationAlternativeCreate(CustomModel):
    """Schema for creating translation alternatives."""
    
    translated_text: str = Field(..., description="Alternative translated text")
    confidence_score: Optional[float] = Field(None, ge=0, le=1, description="Confidence score")
    quality_score: Optional[float] = Field(None, ge=0, le=1, description="Quality score")
    rank: int = Field(..., ge=1, description="Ranking of this alternative")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class TranslationAlternativeResponse(BaseSchema):
    """Schema for translation alternative response."""
    
    job_id: str = Field(..., description="Translation job ID")
    translated_text: str = Field(..., description="Alternative translated text")
    confidence_score: Optional[float] = Field(None, description="Confidence score")
    quality_score: Optional[float] = Field(None, description="Quality score")
    rank: int = Field(..., description="Ranking of this alternative")
    is_selected: bool = Field(..., description="Whether this alternative is selected")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


# Translation Template schemas
class TranslationTemplateCreate(CustomModel):
    """Schema for creating translation templates."""
    
    name: str = Field(..., min_length=1, max_length=255, description="Template name")
    description: Optional[str] = Field(None, description="Template description")
    source_language: str = Field(..., description="Source language")
    target_language: str = Field(..., description="Target language")
    source_pattern: str = Field(..., description="Source text pattern")
    target_pattern: str = Field(..., description="Target text pattern")
    category: Optional[str] = Field(None, max_length=100, description="Template category")
    tags: Optional[List[str]] = Field(None, description="Template tags")
    
    @validator("source_language", "target_language")
    def validate_language(cls, v):
        if v not in SUPPORTED_LANGUAGES:
            raise ValueError(f"Unsupported language: {v}")
        return v


class TranslationTemplateUpdate(CustomModel):
    """Schema for updating translation templates."""
    
    name: Optional[str] = Field(None, min_length=1, max_length=255, description="Template name")
    description: Optional[str] = Field(None, description="Template description")
    source_pattern: Optional[str] = Field(None, description="Source text pattern")
    target_pattern: Optional[str] = Field(None, description="Target text pattern")
    category: Optional[str] = Field(None, max_length=100, description="Template category")
    tags: Optional[List[str]] = Field(None, description="Template tags")


class TranslationTemplateResponse(BaseSchema):
    """Schema for translation template response."""
    
    name: str = Field(..., description="Template name")
    description: Optional[str] = Field(None, description="Template description")
    source_language: str = Field(..., description="Source language")
    target_language: str = Field(..., description="Target language")
    source_pattern: str = Field(..., description="Source text pattern")
    target_pattern: str = Field(..., description="Target text pattern")
    usage_count: int = Field(default=0, description="Number of times used")
    category: Optional[str] = Field(None, description="Template category")
    tags: Optional[List[str]] = Field(None, description="Template tags")
    average_quality_score: Optional[float] = Field(None, description="Average quality score")


# Detailed translation job response with alternatives
class TranslationJobDetailResponse(TranslationJobResponse):
    """Schema for detailed translation job response with alternatives."""
    
    alternatives: List[TranslationAlternativeResponse] = Field(default=[], description="Translation alternatives")


# Translation processing request
class TranslationProcessRequest(CustomModel):
    """Schema for translation processing request."""
    
    text_region_id: str = Field(..., description="Text region ID")
    provider: Optional[LLMProvider] = Field(None, description="LLM provider to use")
    source_language: Optional[str] = Field(None, description="Source language")
    target_language: Optional[str] = Field(None, description="Target language")
    custom_prompt: Optional[str] = Field(None, description="Custom prompt for translation")
    generate_alternatives: bool = Field(default=False, description="Generate alternative translations")
    max_alternatives: int = Field(default=3, ge=1, le=10, description="Maximum number of alternatives")


# Batch translation request
class TranslationBatchProcessRequest(CustomModel):
    """Schema for batch translation processing request."""
    
    text_region_ids: List[str] = Field(..., description="List of text region IDs")
    provider: Optional[LLMProvider] = Field(None, description="LLM provider to use")
    source_language: Optional[str] = Field(None, description="Source language")
    target_language: Optional[str] = Field(None, description="Target language")
    custom_prompt: Optional[str] = Field(None, description="Custom prompt for translation")
    generate_alternatives: bool = Field(default=False, description="Generate alternative translations")


# Translation statistics
class TranslationStatistics(CustomModel):
    """Schema for translation processing statistics."""
    
    total_jobs: int = Field(..., description="Total number of translation jobs")
    completed_jobs: int = Field(..., description="Number of completed jobs")
    failed_jobs: int = Field(..., description="Number of failed jobs")
    pending_jobs: int = Field(..., description="Number of pending jobs")
    processing_jobs: int = Field(..., description="Number of jobs in progress")
    average_processing_time: Optional[float] = Field(None, description="Average processing time in seconds")
    average_confidence_score: Optional[float] = Field(None, description="Average confidence score")
    average_quality_score: Optional[float] = Field(None, description="Average quality score")
    language_pairs: Dict[str, int] = Field(default={}, description="Translation counts by language pair")


# LLM response format for structured translation results
class LLMTranslationResponse(CustomModel):
    """Schema for LLM translation response."""
    
    translated_text: str = Field(..., description="Translated text")
    confidence: Optional[float] = Field(None, ge=0, le=1, description="Translation confidence")
    alternatives: Optional[List[str]] = Field(None, description="Alternative translations")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")
