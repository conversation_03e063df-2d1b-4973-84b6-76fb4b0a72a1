"""
Business logic for translation processing.
"""
import json
import time
from typing import List, Optional, Dict, Any
from uuid import uuid4

from databases import Database
from sqlalchemy import select, insert, update, delete, func, and_

from src.translation.models import TranslationJob, TranslationAlternative, TranslationTemplate
from src.translation.schemas import (
    TranslationJobCreate, TranslationJobResponse, TranslationJobDetailResponse,
    TranslationAlternativeCreate, TranslationAlternativeResponse,
    TranslationTemplateCreate, TranslationTemplateUpdate, TranslationTemplateResponse,
    TranslationProcessRequest, TranslationBatchProcessRequest,
    TranslationStatistics, LLMTranslationResponse
)
from src.translation.exceptions import TranslationJobNotFound, TranslationTemplateNotFound, TranslationFailed
from src.constants import TranslationStatus, LLMProvider, DEFAULT_TRANSLATION_PROMPT
from src.config import settings


class TranslationService:
    """Service class for translation processing operations."""

    def __init__(self, database: Database):
        self.database = database

    async def create_translation_job(self, job_data: TranslationJobCreate) -> TranslationJobResponse:
        """Create a new translation job."""
        job_id = str(uuid4())

        # Build prompt
        prompt = job_data.custom_prompt or DEFAULT_TRANSLATION_PROMPT.format(
            source_language=job_data.source_language,
            target_language=job_data.target_language,
            text=job_data.original_text,
            text_type="unknown"  # TODO: Get from text region
        )

        query = insert(TranslationJob).values(
            id=job_id,
            text_region_id=job_data.text_region_id,
            provider=job_data.provider,
            source_language=job_data.source_language,
            target_language=job_data.target_language,
            original_text=job_data.original_text,
            prompt_used=prompt,
            status=TranslationStatus.PENDING
        )

        await self.database.execute(query)
        return await self.get_translation_job(job_id)

    async def get_translation_job(self, job_id: str) -> TranslationJobResponse:
        """Get a translation job by ID."""
        query = select(TranslationJob).where(TranslationJob.id == job_id)
        result = await self.database.fetch_one(query)

        if not result:
            raise TranslationJobNotFound(
                f"Translation job with ID {job_id} not found")

        return TranslationJobResponse(**dict(result))

    async def get_translation_jobs_by_region(self, text_region_id: str) -> List[TranslationJobResponse]:
        """Get all translation jobs for a text region."""
        query = select(TranslationJob).where(
            TranslationJob.text_region_id == text_region_id
        ).order_by(TranslationJob.created_at.desc())

        results = await self.database.fetch_all(query)
        return [TranslationJobResponse(**dict(result)) for result in results]

    async def get_translation_job_detail(self, job_id: str) -> TranslationJobDetailResponse:
        """Get detailed translation job information with alternatives."""
        job = await self.get_translation_job(job_id)
        alternatives = await self.get_translation_alternatives(job_id)

        return TranslationJobDetailResponse(
            **job.model_dump(),
            alternatives=alternatives
        )

    async def get_translation_alternatives(self, job_id: str) -> List[TranslationAlternativeResponse]:
        """Get all translation alternatives for a job."""
        query = select(TranslationAlternative).where(
            TranslationAlternative.job_id == job_id
        ).order_by(TranslationAlternative.rank)

        results = await self.database.fetch_all(query)
        alternatives = []

        for result in results:
            alternative = TranslationAlternativeResponse(**dict(result))
            # Convert string to boolean for is_selected
            alternative.is_selected = result["is_selected"] == "true"
            alternatives.append(alternative)

        return alternatives

    async def update_translation_job_status(
        self,
        job_id: str,
        status: TranslationStatus,
        translated_text: Optional[str] = None,
        error_message: Optional[str] = None,
        processing_time: Optional[float] = None,
        confidence_score: Optional[float] = None,
        quality_score: Optional[float] = None
    ) -> TranslationJobResponse:
        """Update translation job status and results."""
        update_data = {"status": status}

        if translated_text:
            update_data["translated_text"] = translated_text

        if error_message:
            update_data["error_message"] = error_message

        if processing_time:
            update_data["processing_time_seconds"] = processing_time

        if confidence_score:
            update_data["confidence_score"] = confidence_score

        if quality_score:
            update_data["quality_score"] = quality_score

        query = update(TranslationJob).where(
            TranslationJob.id == job_id).values(**update_data)
        await self.database.execute(query)

        return await self.get_translation_job(job_id)

    async def save_translation_alternatives(
        self,
        job_id: str,
        alternatives: List[TranslationAlternativeCreate]
    ) -> List[TranslationAlternativeResponse]:
        """Save translation alternatives for a job."""
        saved_alternatives = []

        for alt_data in alternatives:
            alt_id = str(uuid4())

            query = insert(TranslationAlternative).values(
                id=alt_id,
                job_id=job_id,
                translated_text=alt_data.translated_text,
                confidence_score=alt_data.confidence_score,
                quality_score=alt_data.quality_score,
                rank=alt_data.rank,
                is_selected="false",  # Default to not selected
                metadata=alt_data.metadata
            )

            await self.database.execute(query)

            # Get the saved alternative
            alt_query = select(TranslationAlternative).where(
                TranslationAlternative.id == alt_id)
            saved_alt = await self.database.fetch_one(alt_query)

            alternative = TranslationAlternativeResponse(**dict(saved_alt))
            alternative.is_selected = False
            saved_alternatives.append(alternative)

        return saved_alternatives

    async def select_translation_alternative(self, job_id: str, alternative_id: str) -> TranslationJobResponse:
        """Select a translation alternative as the primary translation."""
        # First, deselect all alternatives for this job
        deselect_query = update(TranslationAlternative).where(
            TranslationAlternative.job_id == job_id
        ).values(is_selected="false")
        await self.database.execute(deselect_query)

        # Select the specified alternative
        select_query = update(TranslationAlternative).where(
            and_(
                TranslationAlternative.job_id == job_id,
                TranslationAlternative.id == alternative_id
            )
        ).values(is_selected="true")
        await self.database.execute(select_query)

        # Get the selected alternative text and update the job
        alt_query = select(TranslationAlternative).where(
            TranslationAlternative.id == alternative_id)
        alternative = await self.database.fetch_one(alt_query)

        if alternative:
            job_update_query = update(TranslationJob).where(
                TranslationJob.id == job_id
            ).values(translated_text=alternative["translated_text"])
            await self.database.execute(job_update_query)

        return await self.get_translation_job(job_id)

    async def process_translation_request(self, request: TranslationProcessRequest) -> TranslationJobResponse:
        """Process a translation request."""
        # Get text region to extract original text
        # TODO: Implement text region lookup

        # Create translation job
        job_data = TranslationJobCreate(
            text_region_id=request.text_region_id,
            provider=request.provider or LLMProvider.CLAUDE,
            source_language=request.source_language or settings.SOURCE_LANGUAGE,
            target_language=request.target_language or settings.TARGET_LANGUAGE,
            original_text="placeholder",  # TODO: Get from text region
            custom_prompt=request.custom_prompt
        )

        job = await self.create_translation_job(job_data)

        # Start processing (this would be done asynchronously in a real implementation)
        await self.update_translation_job_status(job.id, TranslationStatus.IN_PROGRESS)

        return job

    # Translation Template methods
    async def create_translation_template(self, template_data: TranslationTemplateCreate) -> TranslationTemplateResponse:
        """Create a new translation template."""
        template_id = str(uuid4())

        query = insert(TranslationTemplate).values(
            id=template_id,
            name=template_data.name,
            description=template_data.description,
            source_language=template_data.source_language,
            target_language=template_data.target_language,
            source_pattern=template_data.source_pattern,
            target_pattern=template_data.target_pattern,
            category=template_data.category,
            tags=template_data.tags
        )

        await self.database.execute(query)
        return await self.get_translation_template(template_id)

    async def get_translation_template(self, template_id: str) -> TranslationTemplateResponse:
        """Get a translation template by ID."""
        query = select(TranslationTemplate).where(
            TranslationTemplate.id == template_id)
        result = await self.database.fetch_one(query)

        if not result:
            raise TranslationTemplateNotFound(
                f"Translation template with ID {template_id} not found")

        return TranslationTemplateResponse(**dict(result))

    async def get_translation_templates(
        self,
        source_language: Optional[str] = None,
        target_language: Optional[str] = None,
        category: Optional[str] = None,
        limit: int = 10,
        offset: int = 0
    ) -> List[TranslationTemplateResponse]:
        """Get translation templates with filtering."""
        query = select(TranslationTemplate)

        if source_language:
            query = query.where(
                TranslationTemplate.source_language == source_language)

        if target_language:
            query = query.where(
                TranslationTemplate.target_language == target_language)

        if category:
            query = query.where(TranslationTemplate.category == category)

        query = query.order_by(TranslationTemplate.usage_count.desc()).limit(
            limit).offset(offset)

        results = await self.database.fetch_all(query)
        return [TranslationTemplateResponse(**dict(result)) for result in results]

    async def update_translation_template(
        self,
        template_id: str,
        template_data: TranslationTemplateUpdate
    ) -> TranslationTemplateResponse:
        """Update a translation template."""
        # Check if template exists
        await self.get_translation_template(template_id)

        # Build update data
        update_data = template_data.model_dump(exclude_unset=True)
        if not update_data:
            return await self.get_translation_template(template_id)

        query = update(TranslationTemplate).where(
            TranslationTemplate.id == template_id).values(**update_data)
        await self.database.execute(query)

        return await self.get_translation_template(template_id)

    async def delete_translation_template(self, template_id: str) -> bool:
        """Delete a translation template."""
        # Check if template exists
        await self.get_translation_template(template_id)

        query = delete(TranslationTemplate).where(
            TranslationTemplate.id == template_id)
        await self.database.execute(query)
        return True

    async def get_translation_statistics(self, project_id: Optional[str] = None) -> TranslationStatistics:
        """Get translation processing statistics."""
        base_query = select(TranslationJob)

        if project_id:
            # Join with TextRegion and ProjectPage to filter by project
            from src.projects.models import TextRegion, ProjectPage
            base_query = base_query.join(TextRegion).join(
                ProjectPage).where(ProjectPage.project_id == project_id)

        # Count jobs by status
        status_counts = {}
        for status in TranslationStatus:
            count_query = base_query.where(TranslationJob.status == status)
            count = await self.database.fetch_val(select(func.count()).select_from(count_query.subquery()))
            status_counts[status] = count or 0

        # Calculate averages
        avg_time_query = select(func.avg(
            TranslationJob.processing_time_seconds)).select_from(base_query.subquery())
        avg_confidence_query = select(
            func.avg(TranslationJob.confidence_score)).select_from(base_query.subquery())
        avg_quality_query = select(
            func.avg(TranslationJob.quality_score)).select_from(base_query.subquery())

        avg_time = await self.database.fetch_val(avg_time_query)
        avg_confidence = await self.database.fetch_val(avg_confidence_query)
        avg_quality = await self.database.fetch_val(avg_quality_query)

        # Get language pair statistics
        lang_pair_query = select(
            func.concat(TranslationJob.source_language, " -> ",
                        TranslationJob.target_language).label("pair"),
            func.count().label("count")
        ).select_from(base_query.subquery()).group_by("pair")

        lang_pairs = await self.database.fetch_all(lang_pair_query)
        language_pairs = {pair["pair"]: pair["count"] for pair in lang_pairs}

        return TranslationStatistics(
            total_jobs=sum(status_counts.values()),
            completed_jobs=status_counts.get(TranslationStatus.COMPLETED, 0),
            failed_jobs=status_counts.get(TranslationStatus.FAILED, 0),
            pending_jobs=status_counts.get(TranslationStatus.PENDING, 0),
            processing_jobs=status_counts.get(
                TranslationStatus.IN_PROGRESS, 0),
            average_processing_time=avg_time,
            average_confidence_score=avg_confidence,
            average_quality_score=avg_quality,
            language_pairs=language_pairs
        )
