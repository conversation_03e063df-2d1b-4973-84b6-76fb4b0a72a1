"""
Pydantic schemas for LLM provider operations.
"""
from typing import List, Optional, Dict, Any

from pydantic import Field, validator

from src.constants import LLMProvider
from src.schemas import CustomModel


# Request schemas
class LLMTextGenerationRequest(CustomModel):
    """Schema for text generation request."""
    
    prompt: str = Field(..., description="Text prompt for generation")
    provider: Optional[LLMProvider] = Field(None, description="LLM provider to use")
    model: Optional[str] = Field(None, description="Specific model to use")
    system_message: Optional[str] = Field(None, description="System message for context")
    temperature: float = Field(default=0.7, ge=0.0, le=2.0, description="Generation temperature")
    max_tokens: Optional[int] = Field(None, ge=1, le=8192, description="Maximum tokens to generate")


class LLMImageAnalysisRequest(CustomModel):
    """Schema for image analysis request."""
    
    prompt: str = Field(..., description="Analysis prompt")
    provider: Optional[LLMProvider] = Field(None, description="LLM provider to use")
    model: Optional[str] = Field(None, description="Specific model to use")
    system_message: Optional[str] = Field(None, description="System message for context")
    temperature: float = Field(default=0.7, ge=0.0, le=2.0, description="Generation temperature")


class LLMOCRRequest(CustomModel):
    """Schema for LLM-based OCR request."""
    
    provider: Optional[LLMProvider] = Field(None, description="LLM provider to use")
    model: Optional[str] = Field(None, description="Specific model to use")
    custom_prompt: Optional[str] = Field(None, description="Custom OCR prompt")
    temperature: float = Field(default=0.3, ge=0.0, le=1.0, description="Generation temperature")


class LLMTranslationRequest(CustomModel):
    """Schema for LLM-based translation request."""
    
    text: str = Field(..., description="Text to translate")
    source_language: str = Field(..., description="Source language")
    target_language: str = Field(..., description="Target language")
    text_type: str = Field(default="unknown", description="Type of text (dialogue, narration, etc.)")
    provider: Optional[LLMProvider] = Field(None, description="LLM provider to use")
    model: Optional[str] = Field(None, description="Specific model to use")
    custom_prompt: Optional[str] = Field(None, description="Custom translation prompt")
    generate_alternatives: bool = Field(default=False, description="Generate alternative translations")
    temperature: float = Field(default=0.7, ge=0.0, le=1.0, description="Generation temperature")


# Response schemas
class LLMUsageInfo(CustomModel):
    """Schema for LLM usage information."""
    
    prompt_tokens: Optional[int] = Field(None, description="Number of prompt tokens")
    completion_tokens: Optional[int] = Field(None, description="Number of completion tokens")
    total_tokens: Optional[int] = Field(None, description="Total number of tokens")


class LLMGenerationResponse(CustomModel):
    """Schema for LLM generation response."""
    
    content: str = Field(..., description="Generated content")
    provider: LLMProvider = Field(..., description="LLM provider used")
    model: str = Field(..., description="Model used")
    usage: Optional[LLMUsageInfo] = Field(None, description="Token usage information")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")
    processing_time_ms: Optional[float] = Field(None, description="Processing time in milliseconds")


# Provider information schemas
class LLMModelInfo(CustomModel):
    """Schema for LLM model information."""
    
    name: str = Field(..., description="Model name")
    provider: LLMProvider = Field(..., description="Provider")
    supports_vision: bool = Field(default=False, description="Whether model supports image input")
    max_tokens: Optional[int] = Field(None, description="Maximum token limit")
    description: Optional[str] = Field(None, description="Model description")


class LLMProviderInfo(CustomModel):
    """Schema for LLM provider information."""
    
    provider: LLMProvider = Field(..., description="Provider name")
    is_available: bool = Field(..., description="Whether provider is available")
    default_model: Optional[str] = Field(None, description="Default model for this provider")
    available_models: List[str] = Field(default=[], description="List of available models")
    supports_vision: bool = Field(default=False, description="Whether provider supports vision models")
    api_key_configured: bool = Field(..., description="Whether API key is configured")


class LLMProvidersStatus(CustomModel):
    """Schema for overall LLM providers status."""
    
    providers: List[LLMProviderInfo] = Field(..., description="List of provider information")
    default_provider: Optional[LLMProvider] = Field(None, description="Default provider")
    total_available: int = Field(..., description="Number of available providers")


# Batch processing schemas
class LLMBatchTextRequest(CustomModel):
    """Schema for batch text generation request."""
    
    prompts: List[str] = Field(..., description="List of prompts to process")
    provider: Optional[LLMProvider] = Field(None, description="LLM provider to use")
    model: Optional[str] = Field(None, description="Specific model to use")
    system_message: Optional[str] = Field(None, description="System message for context")
    temperature: float = Field(default=0.7, ge=0.0, le=2.0, description="Generation temperature")
    max_tokens: Optional[int] = Field(None, ge=1, le=8192, description="Maximum tokens to generate")
    
    @validator("prompts")
    def validate_prompts(cls, v):
        if len(v) == 0:
            raise ValueError("At least one prompt is required")
        if len(v) > 100:
            raise ValueError("Maximum 100 prompts allowed per batch")
        return v


class LLMBatchResponse(CustomModel):
    """Schema for batch processing response."""
    
    results: List[LLMGenerationResponse] = Field(..., description="List of generation results")
    total_requests: int = Field(..., description="Total number of requests")
    successful_requests: int = Field(..., description="Number of successful requests")
    failed_requests: int = Field(..., description="Number of failed requests")
    total_processing_time_ms: Optional[float] = Field(None, description="Total processing time")


# Error schemas
class LLMErrorResponse(CustomModel):
    """Schema for LLM error response."""
    
    error_type: str = Field(..., description="Type of error")
    error_message: str = Field(..., description="Error message")
    provider: Optional[LLMProvider] = Field(None, description="Provider that caused the error")
    error_code: Optional[str] = Field(None, description="Provider-specific error code")
    retry_after: Optional[int] = Field(None, description="Seconds to wait before retrying")


# Configuration schemas
class LLMProviderConfig(CustomModel):
    """Schema for LLM provider configuration."""
    
    provider: LLMProvider = Field(..., description="Provider name")
    api_key: str = Field(..., description="API key for the provider")
    default_model: Optional[str] = Field(None, description="Default model to use")
    base_url: Optional[str] = Field(None, description="Custom base URL for API")
    timeout: int = Field(default=60, ge=1, le=300, description="Request timeout in seconds")
    max_retries: int = Field(default=3, ge=0, le=10, description="Maximum number of retries")
    rate_limit: Optional[int] = Field(None, ge=1, description="Rate limit per minute")


class LLMGlobalConfig(CustomModel):
    """Schema for global LLM configuration."""
    
    default_provider: LLMProvider = Field(..., description="Default provider to use")
    fallback_providers: List[LLMProvider] = Field(default=[], description="Fallback providers in order")
    enable_caching: bool = Field(default=True, description="Enable response caching")
    cache_ttl: int = Field(default=3600, ge=60, description="Cache TTL in seconds")
    enable_logging: bool = Field(default=True, description="Enable request/response logging")
    log_level: str = Field(default="INFO", description="Logging level")
    
    @validator("fallback_providers")
    def validate_fallback_providers(cls, v, values):
        if "default_provider" in values and values["default_provider"] in v:
            raise ValueError("Default provider cannot be in fallback providers list")
        return v
