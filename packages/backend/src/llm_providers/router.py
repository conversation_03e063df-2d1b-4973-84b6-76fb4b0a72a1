"""
FastAPI router for LLM provider endpoints.
"""
import time
from typing import List

from fastapi import APIRouter, Depends, status, UploadFile, File, HTTPException

from src.llm_providers.service import llm_service
from src.llm_providers.schemas import (
    LLMTextGenerationRequest,
    LLMImageAnalysisRequest,
    LLMOCRRequest,
    LLMTranslationRequest,
    LLMGenerationResponse,
    LLMProvidersStatus,
    LLMProviderInfo,
    LLMBatchTextRequest,
    LLMBatchResponse
)
from src.llm_providers.factory import LLMProviderFactory
from src.constants import LLMProvider
from src.config import settings

router = APIRouter()


# Provider information endpoints
@router.get(
    "/providers",
    response_model=LLMProvidersStatus,
    summary="Get LLM providers status",
    description="Get information about available LLM providers and their status"
)
async def get_providers_status():
    """Get LLM providers status."""
    providers = []
    
    for provider in LLMProvider:
        is_available = LLMProviderFactory.is_provider_available(provider)
        
        if is_available:
            try:
                client = LLMProviderFactory.create_client(provider)
                default_model = client.get_default_model()
                available_models = client.get_available_models()
                supports_vision = True  # All our providers support vision
            except Exception:
                default_model = None
                available_models = []
                supports_vision = False
        else:
            default_model = None
            available_models = []
            supports_vision = False
        
        provider_info = LLMProviderInfo(
            provider=provider,
            is_available=is_available,
            default_model=default_model,
            available_models=available_models,
            supports_vision=supports_vision,
            api_key_configured=is_available
        )
        providers.append(provider_info)
    
    available_providers = [p for p in providers if p.is_available]
    default_provider = None
    
    try:
        default_provider = LLMProviderFactory.get_default_provider()
    except ValueError:
        pass
    
    return LLMProvidersStatus(
        providers=providers,
        default_provider=default_provider,
        total_available=len(available_providers)
    )


@router.get(
    "/providers/{provider}/models",
    response_model=List[str],
    summary="Get available models for provider",
    description="Get list of available models for a specific provider"
)
async def get_provider_models(provider: LLMProvider):
    """Get available models for a provider."""
    if not LLMProviderFactory.is_provider_available(provider):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Provider {provider} is not available or not configured"
        )
    
    try:
        client = LLMProviderFactory.create_client(provider)
        return client.get_available_models()
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get models for provider {provider}: {str(e)}"
        )


# Text generation endpoints
@router.post(
    "/generate/text",
    response_model=LLMGenerationResponse,
    summary="Generate text",
    description="Generate text using LLM"
)
async def generate_text(request: LLMTextGenerationRequest):
    """Generate text using LLM."""
    start_time = time.time()
    
    try:
        response = await llm_service.generate_text(
            prompt=request.prompt,
            provider=request.provider,
            model=request.model,
            system_message=request.system_message,
            temperature=request.temperature,
            max_tokens=request.max_tokens
        )
        
        processing_time = (time.time() - start_time) * 1000
        
        return LLMGenerationResponse(
            content=response.content,
            provider=response.provider,
            model=response.model,
            usage=response.usage,
            metadata=response.metadata,
            processing_time_ms=processing_time
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Text generation failed: {str(e)}"
        )


@router.post(
    "/generate/batch",
    response_model=LLMBatchResponse,
    summary="Generate text in batch",
    description="Generate text for multiple prompts in batch"
)
async def generate_text_batch(request: LLMBatchTextRequest):
    """Generate text for multiple prompts in batch."""
    start_time = time.time()
    results = []
    successful = 0
    failed = 0
    
    for prompt in request.prompts:
        try:
            response = await llm_service.generate_text(
                prompt=prompt,
                provider=request.provider,
                model=request.model,
                system_message=request.system_message,
                temperature=request.temperature,
                max_tokens=request.max_tokens
            )
            
            result = LLMGenerationResponse(
                content=response.content,
                provider=response.provider,
                model=response.model,
                usage=response.usage,
                metadata=response.metadata
            )
            results.append(result)
            successful += 1
            
        except Exception as e:
            # Add error result
            result = LLMGenerationResponse(
                content="",
                provider=request.provider or LLMProvider.CLAUDE,
                model=request.model or "unknown",
                metadata={"error": str(e)}
            )
            results.append(result)
            failed += 1
    
    total_time = (time.time() - start_time) * 1000
    
    return LLMBatchResponse(
        results=results,
        total_requests=len(request.prompts),
        successful_requests=successful,
        failed_requests=failed,
        total_processing_time_ms=total_time
    )


# Image analysis endpoints
@router.post(
    "/analyze/image",
    response_model=LLMGenerationResponse,
    summary="Analyze image",
    description="Analyze an image using LLM"
)
async def analyze_image(
    file: UploadFile = File(...),
    prompt: str = "Describe this image in detail",
    provider: LLMProvider = None,
    model: str = None,
    system_message: str = None,
    temperature: float = 0.7
):
    """Analyze an image using LLM."""
    # Validate file type
    if not file.content_type or not file.content_type.startswith("image/"):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="File must be an image"
        )
    
    # Check file size
    if file.size and file.size > settings.MAX_FILE_SIZE:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"File size exceeds maximum allowed size of {settings.MAX_FILE_SIZE} bytes"
        )
    
    start_time = time.time()
    
    try:
        # Read image data
        image_data = await file.read()
        image_format = file.content_type.split("/")[1]
        
        response = await llm_service.analyze_image(
            image_data=image_data,
            image_format=image_format,
            prompt=prompt,
            provider=provider,
            model=model,
            system_message=system_message
        )
        
        processing_time = (time.time() - start_time) * 1000
        
        return LLMGenerationResponse(
            content=response.content,
            provider=response.provider,
            model=response.model,
            usage=response.usage,
            metadata=response.metadata,
            processing_time_ms=processing_time
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Image analysis failed: {str(e)}"
        )


# OCR endpoint
@router.post(
    "/ocr",
    summary="Perform OCR on image",
    description="Extract text from image using LLM-based OCR"
)
async def perform_ocr(
    file: UploadFile = File(...),
    provider: LLMProvider = None,
    model: str = None,
    custom_prompt: str = None
):
    """Perform OCR on an image."""
    # Validate file type
    if not file.content_type or not file.content_type.startswith("image/"):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="File must be an image"
        )
    
    try:
        # Read image data
        image_data = await file.read()
        image_format = file.content_type.split("/")[1]
        
        response = await llm_service.perform_ocr(
            image_data=image_data,
            image_format=image_format,
            provider=provider,
            custom_prompt=custom_prompt,
            model=model
        )
        
        return response
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"OCR failed: {str(e)}"
        )


# Translation endpoint
@router.post(
    "/translate",
    summary="Translate text",
    description="Translate text using LLM"
)
async def translate_text(request: LLMTranslationRequest):
    """Translate text using LLM."""
    try:
        response = await llm_service.perform_translation(
            text=request.text,
            source_language=request.source_language,
            target_language=request.target_language,
            text_type=request.text_type,
            provider=request.provider,
            custom_prompt=request.custom_prompt,
            model=request.model,
            generate_alternatives=request.generate_alternatives
        )
        
        return response
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Translation failed: {str(e)}"
        )
