"""
Factory for creating LLM provider clients.
"""
from typing import Dict, Type, Optional

from src.constants import LLMProvider
from src.config import settings
from src.llm_providers.base import Base<PERSON>MClient
from src.llm_providers.claude import <PERSON><PERSON>lient
from src.llm_providers.openai import OpenAIClient
from src.llm_providers.gemini import GeminiClient


class LLMProviderFactory:
    """Factory for creating LLM provider clients."""
    
    _providers: Dict[LLMProvider, Type[BaseLLMClient]] = {
        LLMProvider.CLAUDE: ClaudeClient,
        LLMProvider.OPENAI: OpenAIClient,
        LLMProvider.GEMINI: GeminiClient,
    }
    
    _api_keys: Dict[LLMProvider, str] = {
        LLMProvider.CLAUDE: settings.ANTHROPIC_API_KEY,
        LLMProvider.OPENAI: settings.OPENAI_API_KEY,
        LLMProvider.GEMINI: settings.GOOGLE_API_KEY,
    }
    
    @classmethod
    def create_client(cls, provider: LLMProvider, **kwargs) -> BaseLLMClient:
        """Create an LLM client for the specified provider."""
        if provider not in cls._providers:
            raise ValueError(f"Unsupported LLM provider: {provider}")
        
        api_key = cls._api_keys.get(provider)
        if not api_key:
            raise ValueError(f"API key not configured for provider: {provider}")
        
        client_class = cls._providers[provider]
        return client_class(api_key=api_key, **kwargs)
    
    @classmethod
    def get_available_providers(cls) -> list[LLMProvider]:
        """Get list of available providers with configured API keys."""
        available = []
        for provider, api_key in cls._api_keys.items():
            if api_key and api_key.strip():
                available.append(provider)
        return available
    
    @classmethod
    def is_provider_available(cls, provider: LLMProvider) -> bool:
        """Check if a provider is available (has API key configured)."""
        api_key = cls._api_keys.get(provider)
        return bool(api_key and api_key.strip())
    
    @classmethod
    def get_default_provider(cls) -> LLMProvider:
        """Get the default provider."""
        # Try to use the configured default provider
        default_provider = getattr(settings, 'DEFAULT_OCR_PROVIDER', 'claude')
        try:
            provider = LLMProvider(default_provider)
            if cls.is_provider_available(provider):
                return provider
        except ValueError:
            pass
        
        # Fall back to the first available provider
        available = cls.get_available_providers()
        if available:
            return available[0]
        
        raise ValueError("No LLM providers are configured with API keys")


class LLMClientManager:
    """Manager for LLM client instances with connection pooling."""
    
    def __init__(self):
        self._clients: Dict[LLMProvider, BaseLLMClient] = {}
    
    def get_client(self, provider: Optional[LLMProvider] = None) -> BaseLLMClient:
        """Get or create an LLM client for the specified provider."""
        if provider is None:
            provider = LLMProviderFactory.get_default_provider()
        
        if provider not in self._clients:
            self._clients[provider] = LLMProviderFactory.create_client(provider)
        
        return self._clients[provider]
    
    async def close_all(self):
        """Close all client connections."""
        for client in self._clients.values():
            if hasattr(client, 'close'):
                await client.close()
        self._clients.clear()
    
    def get_available_providers(self) -> list[LLMProvider]:
        """Get list of available providers."""
        return LLMProviderFactory.get_available_providers()
    
    def is_provider_available(self, provider: LLMProvider) -> bool:
        """Check if a provider is available."""
        return LLMProviderFactory.is_provider_available(provider)


# Global client manager instance
llm_client_manager = LLMClientManager()
