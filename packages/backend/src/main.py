"""
FastAPI application entry point for ho-trans manga translation tool.
"""
from contextlib import asynccontextmanager
from typing import AsyncGenerator

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from src.config import settings
from src.database import database
from src.translation.router import router as translation_router
from src.ocr.router import router as ocr_router
from src.projects.router import router as projects_router
from src.llm_providers.router import router as llm_router


@asynccontextmanager
async def lifespan(_app: FastAPI) -> AsyncGenerator[None, None]:
    """Application lifespan manager for startup and shutdown events."""
    # Startup
    await database.connect()
    yield
    # Shutdown
    await database.disconnect()


def create_app() -> FastAPI:
    """Create and configure the FastAPI application."""
    app_configs = {
        "title": "ho-trans API",
        "description": "Manga translation tool with LLM-based OCR and translation",
        "version": "0.1.0",
        "lifespan": lifespan,
    }

    # Hide docs in production
    if settings.ENVIRONMENT not in settings.SHOW_DOCS_ENVIRONMENT:
        app_configs["openapi_url"] = None

    app = FastAPI(**app_configs)

    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.CORS_ORIGINS,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Include routers
    app.include_router(
        projects_router,
        prefix="/api/v1/projects",
        tags=["Projects"]
    )
    app.include_router(
        ocr_router,
        prefix="/api/v1/ocr",
        tags=["OCR"]
    )
    app.include_router(
        translation_router,
        prefix="/api/v1/translation",
        tags=["Translation"]
    )
    app.include_router(
        llm_router,
        prefix="/api/v1/llm",
        tags=["LLM Providers"]
    )

    @app.get("/health")
    async def health_check():
        """Health check endpoint."""
        return {"status": "healthy", "version": app_configs["version"]}

    return app


app = create_app()

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
