"""
Database initialization script for ho-trans.
"""
import asyncio
import os
from pathlib import Path

from alembic import command
from alembic.config import Config
from sqlalchemy import create_engine, text
from sqlalchemy.ext.asyncio import create_async_engine

from src.config import settings
from src.database import Base, metadata


async def init_database():
    """Initialize the database with tables and initial data."""
    print("Initializing ho-trans database...")
    
    # Get database path
    db_path = settings.DATABASE_URL.replace("sqlite:///", "")
    db_dir = Path(db_path).parent
    
    # Create directory if it doesn't exist
    db_dir.mkdir(parents=True, exist_ok=True)
    
    # Create database file if it doesn't exist
    if not Path(db_path).exists():
        print(f"Creating database file: {db_path}")
        Path(db_path).touch()
    
    # Run Alembic migrations
    print("Running database migrations...")
    alembic_cfg = Config("alembic.ini")
    alembic_cfg.set_main_option("sqlalchemy.url", settings.DATABASE_URL)
    
    try:
        # Upgrade to latest migration
        command.upgrade(alembic_cfg, "head")
        print("Database migrations completed successfully!")
    except Exception as e:
        print(f"Migration error: {e}")
        # If migrations fail, create tables directly
        print("Creating tables directly...")
        engine = create_engine(settings.DATABASE_URL)
        Base.metadata.create_all(engine)
        engine.dispose()
    
    print("Database initialization completed!")


async def reset_database():
    """Reset the database by dropping all tables and recreating them."""
    print("Resetting ho-trans database...")
    
    # Get database path
    db_path = settings.DATABASE_URL.replace("sqlite:///", "")
    
    # Remove existing database file
    if Path(db_path).exists():
        print(f"Removing existing database: {db_path}")
        Path(db_path).unlink()
    
    # Reinitialize
    await init_database()


async def check_database():
    """Check database connection and table existence."""
    print("Checking database connection...")
    
    try:
        # Create async engine for testing
        engine = create_async_engine(settings.DATABASE_URL.replace("sqlite://", "sqlite+aiosqlite://"))
        
        async with engine.connect() as conn:
            # Test connection
            result = await conn.execute(text("SELECT 1"))
            print("✓ Database connection successful")
            
            # Check if tables exist
            tables = [
                "project", "project_page", "text_region",
                "ocr_job", "ocr_result",
                "translation_job", "translation_alternative", "translation_template"
            ]
            
            for table in tables:
                try:
                    result = await conn.execute(text(f"SELECT COUNT(*) FROM {table}"))
                    count = result.scalar()
                    print(f"✓ Table '{table}' exists with {count} records")
                except Exception as e:
                    print(f"✗ Table '{table}' not found or error: {e}")
        
        await engine.dispose()
        
    except Exception as e:
        print(f"✗ Database connection failed: {e}")


async def seed_database():
    """Seed the database with sample data."""
    print("Seeding database with sample data...")
    
    from src.projects.service import ProjectService
    from src.projects.schemas import ProjectCreate
    from src.database import database
    
    try:
        await database.connect()
        
        service = ProjectService(database)
        
        # Create sample project
        sample_project = ProjectCreate(
            name="Sample Manga Translation",
            description="A sample project for testing the manga translation workflow",
            source_language="japanese",
            target_language="english"
        )
        
        project = await service.create_project(sample_project)
        print(f"✓ Created sample project: {project.name} (ID: {project.id})")
        
        await database.disconnect()
        
    except Exception as e:
        print(f"✗ Failed to seed database: {e}")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "init":
            asyncio.run(init_database())
        elif command == "reset":
            asyncio.run(reset_database())
        elif command == "check":
            asyncio.run(check_database())
        elif command == "seed":
            asyncio.run(seed_database())
        else:
            print("Usage: python database_init.py [init|reset|check|seed]")
    else:
        asyncio.run(init_database())
