"""Initial schema for ho-trans manga translation tool

Revision ID: 0001
Revises: 
Create Date: 2025-06-30 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '0001'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create project table
    op.create_table('project',
        sa.Column('id', sa.String(36), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('name', sa.String(255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('status', sa.String(50), nullable=False),
        sa.Column('source_language', sa.String(50), nullable=False),
        sa.Column('target_language', sa.String(50), nullable=False),
        sa.PrimaryKeyConstraint('id', name='project_pkey')
    )
    op.create_index('project_name_idx', 'project', ['name'])

    # Create project_page table
    op.create_table('project_page',
        sa.Column('id', sa.String(36), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('project_id', sa.String(36), nullable=False),
        sa.Column('page_number', sa.Integer(), nullable=False),
        sa.Column('original_filename', sa.String(255), nullable=False),
        sa.Column('file_path', sa.String(500), nullable=False),
        sa.Column('file_size', sa.Integer(), nullable=False),
        sa.Column('image_width', sa.Integer(), nullable=True),
        sa.Column('image_height', sa.Integer(), nullable=True),
        sa.Column('ocr_status', sa.String(50), nullable=False),
        sa.ForeignKeyConstraint(['project_id'], ['project.id'], name='project_page_project_id_fkey'),
        sa.PrimaryKeyConstraint('id', name='project_page_pkey')
    )

    # Create text_region table
    op.create_table('text_region',
        sa.Column('id', sa.String(36), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('page_id', sa.String(36), nullable=False),
        sa.Column('region_type', sa.String(50), nullable=False),
        sa.Column('x', sa.Float(), nullable=False),
        sa.Column('y', sa.Float(), nullable=False),
        sa.Column('width', sa.Float(), nullable=False),
        sa.Column('height', sa.Float(), nullable=False),
        sa.Column('original_text', sa.Text(), nullable=True),
        sa.Column('confidence_score', sa.Float(), nullable=True),
        sa.Column('translated_text', sa.Text(), nullable=True),
        sa.Column('translation_status', sa.String(50), nullable=False),
        sa.Column('font_family', sa.String(100), nullable=True),
        sa.Column('font_size', sa.Integer(), nullable=True),
        sa.Column('font_color', sa.String(7), nullable=True),
        sa.Column('background_color', sa.String(7), nullable=True),
        sa.ForeignKeyConstraint(['page_id'], ['project_page.id'], name='text_region_page_id_fkey'),
        sa.PrimaryKeyConstraint('id', name='text_region_pkey')
    )

    # Create ocr_job table
    op.create_table('ocr_job',
        sa.Column('id', sa.String(36), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('page_id', sa.String(36), nullable=False),
        sa.Column('status', sa.String(50), nullable=False),
        sa.Column('provider', sa.String(50), nullable=False),
        sa.Column('prompt_used', sa.Text(), nullable=True),
        sa.Column('raw_response', sa.Text(), nullable=True),
        sa.Column('processing_time_seconds', sa.Float(), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('retry_count', sa.Integer(), nullable=False),
        sa.Column('total_regions_detected', sa.Integer(), nullable=False),
        sa.Column('average_confidence', sa.Float(), nullable=True),
        sa.ForeignKeyConstraint(['page_id'], ['project_page.id'], name='ocr_job_page_id_fkey'),
        sa.PrimaryKeyConstraint('id', name='ocr_job_pkey')
    )

    # Create ocr_result table
    op.create_table('ocr_result',
        sa.Column('id', sa.String(36), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('job_id', sa.String(36), nullable=False),
        sa.Column('detected_text', sa.Text(), nullable=False),
        sa.Column('confidence_score', sa.Float(), nullable=True),
        sa.Column('region_type', sa.String(50), nullable=True),
        sa.Column('x', sa.Float(), nullable=True),
        sa.Column('y', sa.Float(), nullable=True),
        sa.Column('width', sa.Float(), nullable=True),
        sa.Column('height', sa.Float(), nullable=True),
        sa.Column('metadata', sa.JSON(), nullable=True),
        sa.ForeignKeyConstraint(['job_id'], ['ocr_job.id'], name='ocr_result_job_id_fkey'),
        sa.PrimaryKeyConstraint('id', name='ocr_result_pkey')
    )

    # Create translation_job table
    op.create_table('translation_job',
        sa.Column('id', sa.String(36), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('text_region_id', sa.String(36), nullable=False),
        sa.Column('status', sa.String(50), nullable=False),
        sa.Column('provider', sa.String(50), nullable=False),
        sa.Column('source_language', sa.String(50), nullable=False),
        sa.Column('target_language', sa.String(50), nullable=False),
        sa.Column('original_text', sa.Text(), nullable=False),
        sa.Column('translated_text', sa.Text(), nullable=True),
        sa.Column('prompt_used', sa.Text(), nullable=True),
        sa.Column('raw_response', sa.Text(), nullable=True),
        sa.Column('processing_time_seconds', sa.Float(), nullable=True),
        sa.Column('confidence_score', sa.Float(), nullable=True),
        sa.Column('quality_score', sa.Float(), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('retry_count', sa.Integer(), nullable=False),
        sa.Column('metadata', sa.JSON(), nullable=True),
        sa.ForeignKeyConstraint(['text_region_id'], ['text_region.id'], name='translation_job_text_region_id_fkey'),
        sa.PrimaryKeyConstraint('id', name='translation_job_pkey')
    )

    # Create translation_alternative table
    op.create_table('translation_alternative',
        sa.Column('id', sa.String(36), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('job_id', sa.String(36), nullable=False),
        sa.Column('translated_text', sa.Text(), nullable=False),
        sa.Column('confidence_score', sa.Float(), nullable=True),
        sa.Column('quality_score', sa.Float(), nullable=True),
        sa.Column('rank', sa.Integer(), nullable=False),
        sa.Column('is_selected', sa.String(5), nullable=False),
        sa.Column('metadata', sa.JSON(), nullable=True),
        sa.ForeignKeyConstraint(['job_id'], ['translation_job.id'], name='translation_alternative_job_id_fkey'),
        sa.PrimaryKeyConstraint('id', name='translation_alternative_pkey')
    )

    # Create translation_template table
    op.create_table('translation_template',
        sa.Column('id', sa.String(36), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('name', sa.String(255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('source_language', sa.String(50), nullable=False),
        sa.Column('target_language', sa.String(50), nullable=False),
        sa.Column('source_pattern', sa.Text(), nullable=False),
        sa.Column('target_pattern', sa.Text(), nullable=False),
        sa.Column('usage_count', sa.Integer(), nullable=False),
        sa.Column('category', sa.String(100), nullable=True),
        sa.Column('tags', sa.JSON(), nullable=True),
        sa.Column('average_quality_score', sa.Float(), nullable=True),
        sa.PrimaryKeyConstraint('id', name='translation_template_pkey')
    )


def downgrade() -> None:
    op.drop_table('translation_template')
    op.drop_table('translation_alternative')
    op.drop_table('translation_job')
    op.drop_table('ocr_result')
    op.drop_table('ocr_job')
    op.drop_table('text_region')
    op.drop_table('project_page')
    op.drop_index('project_name_idx', table_name='project')
    op.drop_table('project')
