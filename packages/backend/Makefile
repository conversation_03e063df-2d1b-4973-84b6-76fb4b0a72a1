# Makefile for ho-trans backend development

.PHONY: help install server test lint format typecheck init-db reset-db check-db seed-db migrate docs clean

# Default target
help:
	@echo "Available commands:"
	@echo "  install     - Install development dependencies"
	@echo "  server      - Run development server"
	@echo "  test        - Run tests"
	@echo "  lint        - Run code linting"
	@echo "  format      - Format code"
	@echo "  typecheck   - Run type checking"
	@echo "  init-db     - Initialize database"
	@echo "  reset-db    - Reset database"
	@echo "  check-db    - Check database"
	@echo "  seed-db     - Seed database with sample data"
	@echo "  migrate     - Run database migrations"
	@echo "  docs        - Generate documentation"
	@echo "  clean       - Clean up generated files"

# Install dependencies
install:
	pip install -r requirements/dev.txt

# Run development server
server:
	uvicorn src.main:app --reload --host 0.0.0.0 --port 8000

# Run tests
test:
	pytest tests/ -v --cov=src --cov-report=html

# Run linting
lint:
	ruff check src/
	black --check src/
	isort --check-only src/

# Format code
format:
	ruff check --fix src/
	black src/
	isort src/

# Type checking
typecheck:
	mypy src/

# Database commands
init-db:
	python src/database_init.py init

reset-db:
	python src/database_init.py reset

check-db:
	python src/database_init.py check

seed-db:
	python src/database_init.py seed

migrate:
	alembic upgrade head

# Documentation
docs:
	mkdocs build

serve-docs:
	mkdocs serve

# Clean up
clean:
	find . -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	find . -name "*.pyc" -exec rm -f {} + 2>/dev/null || true
	find . -name "*.pyo" -exec rm -f {} + 2>/dev/null || true
	rm -rf .coverage htmlcov/ .pytest_cache/ .mypy_cache/ dist/ build/ *.egg-info/

# Development workflow
dev-setup: install init-db seed-db
	@echo "Development environment setup complete!"

dev-check: lint typecheck test
	@echo "All checks passed!"
