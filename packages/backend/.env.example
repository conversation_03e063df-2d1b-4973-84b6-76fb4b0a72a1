# Environment Configuration for ho-trans Backend

# Application Environment
ENVIRONMENT=development

# Database Configuration
DATABASE_URL=sqlite:///./ho_trans.db

# CORS Configuration
CORS_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000"]

# LLM Provider API Keys
ANTHROPIC_API_KEY=your_anthropic_api_key_here
OPENAI_API_KEY=your_openai_api_key_here
GOOGLE_API_KEY=your_google_api_key_here

# Default LLM Providers
DEFAULT_OCR_PROVIDER=claude
DEFAULT_TRANSLATION_PROVIDER=claude

# Language Settings
SOURCE_LANGUAGE=japanese
TARGET_LANGUAGE=english

# File Upload Settings
MAX_FILE_SIZE=10485760  # 10MB in bytes
ALLOWED_IMAGE_TYPES=["image/jpeg", "image/png", "image/webp"]
UPLOAD_DIR=./uploads

# API Documentation
SHOW_DOCS_ENVIRONMENT=["development", "staging"]

# Logging
LOG_LEVEL=INFO
