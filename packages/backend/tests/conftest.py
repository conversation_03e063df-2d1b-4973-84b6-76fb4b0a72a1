"""
Pytest configuration and fixtures for ho-trans backend tests.
"""
import asyncio
import pytest
from typing import AsyncGenerator
from unittest.mock import AsyncMock

from databases import Database
from fastapi.testclient import TestClient

from src.main import create_app
from src.config import settings


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
async def mock_database() -> AsyncMock:
    """Create a mock database for testing."""
    mock_db = AsyncMock(spec=Database)
    return mock_db


@pytest.fixture
def test_client() -> TestClient:
    """Create a test client for the FastAPI application."""
    app = create_app()
    return TestClient(app)


@pytest.fixture
def sample_project_data():
    """Sample project data for testing."""
    return {
        "name": "Test Manga Project",
        "description": "A test project for manga translation",
        "source_language": "japanese",
        "target_language": "english"
    }


@pytest.fixture
def sample_page_data():
    """Sample page data for testing."""
    return {
        "page_number": 1,
        "original_filename": "page_001.jpg"
    }


@pytest.fixture
def sample_text_region_data():
    """Sample text region data for testing."""
    return {
        "region_type": "speech_bubble",
        "x": 0.1,
        "y": 0.2,
        "width": 0.3,
        "height": 0.1,
        "original_text": "こんにちは",
        "confidence_score": 0.95
    }


@pytest.fixture
def sample_ocr_job_data():
    """Sample OCR job data for testing."""
    return {
        "page_id": "test-page-id",
        "provider": "claude",
        "custom_prompt": "Extract text from this manga page"
    }


@pytest.fixture
def sample_translation_job_data():
    """Sample translation job data for testing."""
    return {
        "text_region_id": "test-region-id",
        "provider": "claude",
        "source_language": "japanese",
        "target_language": "english",
        "original_text": "こんにちは"
    }


@pytest.fixture
def sample_translation_template_data():
    """Sample translation template data for testing."""
    return {
        "name": "Greeting Template",
        "description": "Template for common greetings",
        "source_language": "japanese",
        "target_language": "english",
        "source_pattern": "こんにちは",
        "target_pattern": "Hello",
        "category": "greetings"
    }


# Mock responses for external services
@pytest.fixture
def mock_llm_response():
    """Mock LLM response for testing."""
    return {
        "content": "Hello, world!",
        "provider": "claude",
        "model": "claude-3-5-sonnet-20241022",
        "usage": {
            "prompt_tokens": 10,
            "completion_tokens": 5,
            "total_tokens": 15
        },
        "metadata": {
            "stop_reason": "end_turn"
        }
    }


@pytest.fixture
def mock_ocr_response():
    """Mock OCR response for testing."""
    return {
        "regions": [
            {
                "text": "こんにちは",
                "type": "speech_bubble",
                "confidence": 0.95,
                "coordinates": {
                    "x": 0.1,
                    "y": 0.2,
                    "width": 0.3,
                    "height": 0.1
                }
            }
        ],
        "metadata": {
            "processing_time": 1.5,
            "model_used": "claude-3-5-sonnet-20241022"
        }
    }


@pytest.fixture
def mock_translation_response():
    """Mock translation response for testing."""
    return {
        "translated_text": "Hello",
        "confidence": 0.9,
        "alternatives": ["Hi", "Greetings"],
        "metadata": {
            "model_used": "claude-3-5-sonnet-20241022",
            "processing_time": 0.8
        }
    }
