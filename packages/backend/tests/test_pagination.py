"""
Tests for pagination utilities.
"""
import pytest
from unittest.mock import AsyncMock, MagicMock

from src.pagination import (
    PaginationParams,
    PaginationMeta,
    PaginatedResponse,
    Paginator,
    create_paginated_response,
    get_pagination_info
)


class TestPaginationParams:
    """Test pagination parameters."""
    
    def test_pagination_params_defaults(self):
        """Test default pagination parameters."""
        params = PaginationParams()
        assert params.page == 1
        assert params.limit == 10
        assert params.offset == 0
    
    def test_pagination_params_custom(self):
        """Test custom pagination parameters."""
        params = PaginationParams(page=3, limit=20)
        assert params.page == 3
        assert params.limit == 20
        assert params.offset == 40  # (3-1) * 20
    
    def test_pagination_params_validation(self):
        """Test pagination parameters validation."""
        # Valid parameters
        params = PaginationParams(page=1, limit=50)
        assert params.page == 1
        assert params.limit == 50
        
        # Test limit validation
        with pytest.raises(ValueError):
            PaginationParams(page=1, limit=101)  # Exceeds max limit


class TestPaginationMeta:
    """Test pagination metadata."""
    
    def test_pagination_meta_creation(self):
        """Test pagination metadata creation."""
        meta = PaginationMeta(
            page=2,
            limit=10,
            total=25,
            pages=3,
            has_prev=True,
            has_next=True,
            prev_page=1,
            next_page=3
        )
        
        assert meta.page == 2
        assert meta.limit == 10
        assert meta.total == 25
        assert meta.pages == 3
        assert meta.has_prev is True
        assert meta.has_next is True
        assert meta.prev_page == 1
        assert meta.next_page == 3


class TestPaginatedResponse:
    """Test paginated response."""
    
    def test_paginated_response_creation(self):
        """Test creating paginated response."""
        items = ["item1", "item2", "item3"]
        params = PaginationParams(page=1, limit=10)
        total = 25
        
        response = PaginatedResponse.create(items, total, params)
        
        assert response.items == items
        assert response.meta.page == 1
        assert response.meta.limit == 10
        assert response.meta.total == 25
        assert response.meta.pages == 3  # ceil(25/10)
        assert response.meta.has_prev is False
        assert response.meta.has_next is True
        assert response.meta.prev_page is None
        assert response.meta.next_page == 2
    
    def test_paginated_response_last_page(self):
        """Test paginated response for last page."""
        items = ["item1", "item2"]
        params = PaginationParams(page=3, limit=10)
        total = 25
        
        response = PaginatedResponse.create(items, total, params)
        
        assert response.meta.page == 3
        assert response.meta.has_prev is True
        assert response.meta.has_next is False
        assert response.meta.prev_page == 2
        assert response.meta.next_page is None
    
    def test_paginated_response_empty(self):
        """Test paginated response with no items."""
        items = []
        params = PaginationParams(page=1, limit=10)
        total = 0
        
        response = PaginatedResponse.create(items, total, params)
        
        assert response.items == []
        assert response.meta.total == 0
        assert response.meta.pages == 1
        assert response.meta.has_prev is False
        assert response.meta.has_next is False


@pytest.mark.asyncio
class TestPaginator:
    """Test database paginator."""
    
    async def test_paginator_basic(self):
        """Test basic pagination functionality."""
        # Mock database and query
        mock_db = AsyncMock()
        mock_query = MagicMock()
        
        # Mock database responses
        mock_db.fetch_val.return_value = 25  # Total count
        mock_db.fetch_all.return_value = [
            {"id": 1, "name": "item1"},
            {"id": 2, "name": "item2"},
            {"id": 3, "name": "item3"}
        ]
        
        paginator = Paginator(mock_db)
        params = PaginationParams(page=1, limit=10)
        
        items, total = await paginator.paginate(mock_query, params)
        
        assert total == 25
        assert len(items) == 3
        assert items[0]["name"] == "item1"
        
        # Verify database calls
        mock_db.fetch_val.assert_called_once()
        mock_db.fetch_all.assert_called_once()
    
    async def test_paginator_response(self):
        """Test paginator response creation."""
        # Mock database and query
        mock_db = AsyncMock()
        mock_query = MagicMock()
        
        # Mock database responses
        mock_db.fetch_val.return_value = 25
        mock_db.fetch_all.return_value = [
            {"id": 1, "name": "item1"},
            {"id": 2, "name": "item2"}
        ]
        
        paginator = Paginator(mock_db)
        params = PaginationParams(page=2, limit=10)
        
        response = await paginator.paginate_response(mock_query, params)
        
        assert isinstance(response, PaginatedResponse)
        assert len(response.items) == 2
        assert response.meta.page == 2
        assert response.meta.total == 25


class TestUtilityFunctions:
    """Test utility functions."""
    
    def test_create_paginated_response(self):
        """Test create_paginated_response utility."""
        items = ["a", "b", "c"]
        response = create_paginated_response(items, 15, 2, 5)
        
        assert response.items == items
        assert response.meta.page == 2
        assert response.meta.limit == 5
        assert response.meta.total == 15
        assert response.meta.pages == 3
    
    def test_get_pagination_info(self):
        """Test get_pagination_info utility."""
        info = get_pagination_info(25, 2, 10)
        
        expected = {
            "page": 2,
            "limit": 10,
            "total": 25,
            "pages": 3,
            "has_prev": True,
            "has_next": True,
            "prev_page": 1,
            "next_page": 3,
        }
        
        assert info == expected
    
    def test_get_pagination_info_edge_cases(self):
        """Test pagination info edge cases."""
        # First page
        info = get_pagination_info(25, 1, 10)
        assert info["has_prev"] is False
        assert info["prev_page"] is None
        
        # Last page
        info = get_pagination_info(25, 3, 10)
        assert info["has_next"] is False
        assert info["next_page"] is None
        
        # Empty result
        info = get_pagination_info(0, 1, 10)
        assert info["pages"] == 1
        assert info["has_prev"] is False
        assert info["has_next"] is False
