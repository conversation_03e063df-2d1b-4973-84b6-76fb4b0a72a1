name: Backend Tests

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'packages/backend/**'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'packages/backend/**'

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.11, 3.12]

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}

    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('packages/backend/requirements/dev.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-

    - name: Install dependencies
      working-directory: packages/backend
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements/dev.txt

    - name: Check test environment
      working-directory: packages/backend
      run: |
        python scripts/test.py check

    - name: Run linting
      working-directory: packages/backend
      run: |
        ruff check src/
        black --check src/
        isort --check-only src/

    - name: Run type checking
      working-directory: packages/backend
      run: |
        mypy src/

    - name: Run unit tests
      working-directory: packages/backend
      run: |
        pytest tests/ -v --cov=src --cov-report=xml --cov-report=term -m "not integration"

    - name: Run integration tests
      working-directory: packages/backend
      run: |
        pytest tests/ -v -m integration

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: packages/backend/coverage.xml
        flags: backend
        name: backend-coverage
        fail_ci_if_error: false

  test-database:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Set up Python 3.11
      uses: actions/setup-python@v4
      with:
        python-version: 3.11

    - name: Install dependencies
      working-directory: packages/backend
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements/dev.txt

    - name: Test database initialization
      working-directory: packages/backend
      run: |
        python src/database_init.py init
        python src/database_init.py check

    - name: Test database migrations
      working-directory: packages/backend
      run: |
        alembic upgrade head
        alembic downgrade base
        alembic upgrade head

  security:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Set up Python 3.11
      uses: actions/setup-python@v4
      with:
        python-version: 3.11

    - name: Install dependencies
      working-directory: packages/backend
      run: |
        python -m pip install --upgrade pip
        pip install safety bandit

    - name: Run safety check
      working-directory: packages/backend
      run: |
        safety check -r requirements/base.txt

    - name: Run bandit security check
      working-directory: packages/backend
      run: |
        bandit -r src/ -f json -o bandit-report.json || true
        bandit -r src/
